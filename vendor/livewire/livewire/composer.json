{"name": "livewire/livewire", "description": "A front-end framework for Laravel.", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "caleb<PERSON><PERSON>@gmail.com"}], "require": {"php": "^8.1", "illuminate/database": "^10.0|^11.0|^12.0", "illuminate/routing": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "illuminate/validation": "^10.0|^11.0|^12.0", "league/mime-type-detection": "^1.9", "symfony/console": "^6.0|^7.0", "symfony/http-kernel": "^6.2|^7.0", "laravel/prompts": "^0.1.24|^0.2|^0.3"}, "require-dev": {"psy/psysh": "^0.11.22|^0.12", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^10.4|^11.5", "laravel/framework": "^10.15.0|^11.0|^12.0", "orchestra/testbench": "^8.21.0|^9.0|^10.0", "orchestra/testbench-dusk": "^8.24|^9.1|^10.0", "calebporzio/sushi": "^2.1"}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Livewire\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\": "vendor/orchestra/testbench-core/laravel/app", "Tests\\": "tests/", "LegacyTests\\": "legacy_tests/"}}, "extra": {"laravel": {"providers": ["Livewire\\LivewireServiceProvider"], "aliases": {"Livewire": "Livewire\\Livewire"}}}, "minimum-stability": "dev", "prefer-stable": true}