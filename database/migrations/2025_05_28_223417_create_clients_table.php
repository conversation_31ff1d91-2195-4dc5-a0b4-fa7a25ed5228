<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('company_name')->nullable();
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('whatsapp')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('Saudi Arabia');
            $table->enum('status', ['prospect', 'active', 'inactive', 'archived'])->default('prospect');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->text('notes')->nullable();
            $table->json('contact_history')->nullable(); // Store communication logs
            $table->json('social_media')->nullable(); // Store social media links
            $table->decimal('total_value', 12, 2)->default(0); // Total project value
            $table->date('first_contact_date')->nullable();
            $table->date('last_contact_date')->nullable();
            $table->string('source')->nullable(); // How they found us
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
