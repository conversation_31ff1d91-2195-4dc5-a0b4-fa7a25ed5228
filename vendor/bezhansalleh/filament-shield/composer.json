{"name": "bezhansalleh/filament-shield", "description": "Filament support for `spatie/laravel-permission`.", "keywords": ["bezhanSalleh", "laravel", "filament", "filament-shield", "permission", "permissions", "roles", "acl", "rbac", "security"], "homepage": "https://github.com/bezhansalleh/filament-shield", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.1", "filament/filament": "^3.2", "spatie/laravel-package-tools": "^1.9", "spatie/laravel-permission": "^6.0"}, "require-dev": {"larastan/larastan": "^2.0", "laravel/pint": "^1.0", "nunomaduro/collision": "^7.0|^8.0", "orchestra/testbench": "^8.0|^9.0", "pestphp/pest": "^2.34", "pestphp/pest-plugin-laravel": "^2.3", "phpstan/extension-installer": "^1.3", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.37"}, "autoload": {"psr-4": {"BezhanSalleh\\FilamentShield\\": "src", "BezhanSalleh\\FilamentShield\\Database\\Factories\\": "database/factories"}}, "autoload-dev": {"psr-4": {"BezhanSalleh\\FilamentShield\\Tests\\": "tests"}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "test": "vendor/bin/pest --no-coverage", "test-coverage": "vendor/bin/pest --coverage", "format": "vendor/bin/pint", "finalize": "composer format && composer analyse && composer test"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider"], "aliases": {"FilamentShield": "BezhanSalleh\\FilamentShield\\Facades\\FilamentShield"}}}, "minimum-stability": "dev", "prefer-stable": true}