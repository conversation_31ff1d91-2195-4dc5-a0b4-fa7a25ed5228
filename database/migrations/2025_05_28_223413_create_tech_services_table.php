<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tech_services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('category_id')->nullable()->constrained('service_categories')->onDelete('set null');
            $table->decimal('price', 10, 2)->nullable();
            $table->string('currency', 3)->default('SAR');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->json('features')->nullable(); // Store service features as JSON
            $table->integer('estimated_hours')->nullable();
            $table->text('requirements')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tech_services');
    }
};
