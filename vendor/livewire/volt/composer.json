{"name": "livewire/volt", "description": "An elegantly crafted functional API for Laravel Livewire.", "keywords": ["laravel", "livewire", "volt"], "homepage": "https://github.com/livewire/volt", "license": "MIT", "support": {"issues": "https://github.com/livewire/volt/issues", "source": "https://github.com/livewire/volt"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "laravel/framework": "^10.38.2|^11.0|^12.0", "livewire/livewire": "^3.6.1"}, "require-dev": {"laravel/folio": "^1.1", "mockery/mockery": "^1.6", "orchestra/testbench": "^8.15.0|^9.0|^10.0", "pestphp/pest": "^2.9.5|^3.0", "phpstan/phpstan": "^1.10"}, "autoload": {"psr-4": {"Livewire\\Volt\\": "src/"}, "files": ["functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Workbench\\App\\": "workbench/app/", "Workbench\\Database\\": "workbench/database/"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}, "laravel": {"providers": ["Livewire\\Volt\\VoltServiceProvider"]}}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"post-autoload-dump": "@composer run prepare", "prepare": "@php vendor/bin/testbench package:discover --ansi", "build": ["@composer run prepare", "@php vendor/bin/testbench workbench:create-sqlite-db", "@php vendor/bin/testbench migrate:refresh"], "start": ["@composer run build", "@php vendor/bin/testbench serve"]}, "minimum-stability": "dev", "prefer-stable": true}