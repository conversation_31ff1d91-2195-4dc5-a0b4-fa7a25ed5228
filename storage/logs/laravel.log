[2025-05-28 22:32:31] local.ERROR: The "--fresh" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--fresh\" option does not exist. at /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php:226)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('fresh', NULL)
#1 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--fresh')
#2 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--fresh', true)
#3 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(BezhanSalleh\\FilamentShield\\Commands\\InstallCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-28 22:33:14] local.ERROR: No default Filament panel is set. You may do this with the `default()` method inside a Filament provider's `panel()` configuration. {"exception":"[object] (Filament\\Exceptions\\NoDefaultPanelSetException(code: 0): No default Filament panel is set. You may do this with the `default()` method inside a Filament provider's `panel()` configuration. at /Users/<USER>/Sites/newlial/vendor/filament/filament/src/Exceptions/NoDefaultPanelSetException.php:17)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/filament/filament/src/PanelRegistry.php(43): Filament\\Exceptions\\NoDefaultPanelSetException::make()
#1 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Collections/helpers.php(236): Filament\\PanelRegistry->{closure:Filament\\PanelRegistry::getDefault():43}()
#2 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(271): value(Object(Closure))
#3 /Users/<USER>/Sites/newlial/vendor/filament/filament/src/PanelRegistry.php(40): Illuminate\\Support\\Arr::first(Array, Object(Closure), Object(Closure))
#4 /Users/<USER>/Sites/newlial/vendor/filament/filament/src/PanelRegistry.php(52): Filament\\PanelRegistry->getDefault()
#5 /Users/<USER>/Sites/newlial/vendor/filament/filament/src/FilamentManager.php(289): Filament\\PanelRegistry->get('lial', true)
#6 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Filament\\FilamentManager->getPanel('lial')
#7 /Users/<USER>/Sites/newlial/vendor/bezhansalleh/filament-shield/src/Commands/InstallCommand.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanel', Array)
#8 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): BezhanSalleh\\FilamentShield\\Commands\\InstallCommand->handle()
#9 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#10 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(BezhanSalleh\\FilamentShield\\Commands\\InstallCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-05-28 22:33:25] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php:226)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-28 22:33:27] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php:226)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-28 22:33:29] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php:226)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 /Users/<USER>/Sites/newlial/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-28 22:34:05] local.ERROR: The panel is required. {"exception":"[object] (Laravel\\Prompts\\Exceptions\\NonInteractiveValidationException(code: 0): The panel is required. at /Users/<USER>/Sites/newlial/vendor/laravel/prompts/src/Concerns/Interactivity.php:32)
[stacktrace]
#0 /Users/<USER>/Sites/newlial/vendor/laravel/prompts/src/Prompt.php(114): Laravel\\Prompts\\Prompt->default()
#1 /Users/<USER>/Sites/newlial/vendor/laravel/prompts/src/helpers.php(14): Laravel\\Prompts\\Prompt->prompt()
#2 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Concerns/PromptsForMissingInput.php(60): Laravel\\Prompts\\text('What is the pan...', '', '', false, Object(Closure))
#3 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php(271): Illuminate\\Console\\Command->{closure:Illuminate\\Console\\Concerns\\PromptsForMissingInput::promptForMissingArguments():48}(Object(Symfony\\Component\\Console\\Input\\InputArgument), 'panel')
#4 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Concerns/PromptsForMissingInput.php(48): Illuminate\\Support\\Collection->each(Object(Closure))
#5 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Concerns/PromptsForMissingInput.php(29): Illuminate\\Console\\Command->promptForMissingArguments(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Sites/newlial/vendor/symfony/console/Command/Command.php(264): Illuminate\\Console\\Command->interact(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(BezhanSalleh\\FilamentShield\\Commands\\InstallCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Sites/newlial/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 /Users/<USER>/Sites/newlial/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 /Users/<USER>/Sites/newlial/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
