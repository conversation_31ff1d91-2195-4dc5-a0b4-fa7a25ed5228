<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['planning', 'in_progress', 'testing', 'completed', 'on_hold', 'cancelled'])->default('planning');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->decimal('budget', 12, 2)->nullable();
            $table->decimal('actual_cost', 12, 2)->default(0);
            $table->string('currency', 3)->default('SAR');

            // Timeline fields
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->date('actual_start_date')->nullable();
            $table->date('actual_end_date')->nullable();

            // Enhanced fields as requested
            $table->json('hosting_details')->nullable(); // Provider, login credentials, server info
            $table->json('domain_details')->nullable(); // Registrar, renewal dates, DNS settings
            $table->json('email_provider')->nullable(); // Provider, admin access
            $table->string('live_url')->nullable(); // Live project URL
            $table->enum('url_status', ['active', 'inactive', 'maintenance'])->default('inactive');
            $table->json('team_members')->nullable(); // Assigned team members with roles
            $table->string('whatsapp_group_link')->nullable(); // WhatsApp group for communication

            // Progress tracking
            $table->integer('progress_percentage')->default(0);
            $table->json('milestones')->nullable(); // Project milestones
            $table->text('notes')->nullable();
            $table->json('requirements')->nullable(); // Project requirements
            $table->json('deliverables')->nullable(); // Project deliverables

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
