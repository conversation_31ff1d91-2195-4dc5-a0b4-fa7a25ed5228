<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.2/phpunit.xsd" backupGlobals="false" bootstrap="vendor/autoload.php" colors="true" processIsolation="false" stopOnFailure="false" executionOrder="random" failOnWarning="true" failOnRisky="true" failOnEmptyTestSuite="true" beStrictAboutOutputDuringTests="true" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <testsuites>
    <testsuite name="BezhanSalleh Test Suite">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
  <coverage>
    <report>
      <html outputDirectory="build/coverage"/>
      <text outputFile="build/coverage.txt"/>
      <clover outputFile="build/logs/clover.xml"/>
    </report>
  </coverage>
  <logging>
    <junit outputFile="build/report.junit.xml"/>
  </logging>
  <source>
    <include>
      <directory suffix=".php">./src</directory>
    </include>
  </source>
</phpunit>
