<?php

namespace {{ namespace }};

use {{ auth_model_fqcn }};

use Illuminate\Auth\Access\HandlesAuthorization;

class {{ modelPolicy }}
{
    use HandlesAuthorization;

    /**
     * Determine whether the {{ auth_model_variable }} can view any models.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function viewAny({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ ViewAny }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can view the model.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function view({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ View }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can create models.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function create({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Create }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can update the model.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function update({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Update }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can delete the model.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function delete({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Delete }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can bulk delete.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function deleteAny({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ DeleteAny }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can permanently delete.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function forceDelete({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ ForceDelete }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can permanently bulk delete.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function forceDeleteAny({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ ForceDeleteAny }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can restore.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function restore({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Restore }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can bulk restore.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function restoreAny({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ RestoreAny }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can bulk restore.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function replicate({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Replicate }}');
    }

    /**
     * Determine whether the {{ auth_model_variable }} can reorder.
     *
     * @param  \{{ auth_model_fqcn }}  ${{ auth_model_variable }}
     * @return bool
     */
    public function reorder({{ auth_model_name }} ${{ auth_model_variable }}): bool
    {
        return ${{ auth_model_variable }}->can('{{ Reorder }}');
    }
}
